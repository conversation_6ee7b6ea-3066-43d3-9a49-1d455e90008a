{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 4918658095350666423, "deps": [[2013030631243296465, "webview2_com", false, 4629790111290470228], [3334271191048661305, "windows_version", false, 13100344006951603514], [3722963349756955755, "once_cell", false, 7508801457861560324], [4143744114649553716, "raw_window_handle", false, 10639854346359357115], [5628259161083531273, "windows_core", false, 7610176989549502652], [7606335748176206944, "dpi", false, 11872213363386873584], [9010263965687315507, "http", false, 7531794789693008200], [9141053277961803901, "build_script_build", false, 3719804497264484758], [10806645703491011684, "thiserror", false, 2502066569376588129], [11989259058781683633, "dunce", false, 4683714416206784932], [14585479307175734061, "windows", false, 16597157699585629547], [16727543399706004146, "cookie", false, 3243518960846693963]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-8c07836d04e9e0ae\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
import React, { useState, useCallback, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentWindow } from '@tauri-apps/api/window';
import './App.css';

interface WindowState {
  isMinimized: boolean;
  isMuted: boolean;
}

const App: React.FC = () => {
  const [windowState, setWindowState] = useState<WindowState>({
    isMinimized: false,
    isMuted: false
  });
  const [error, setError] = useState<string | null>(null);
  const [loadingStates, setLoadingStates] = useState({
    youtube: true,
    spotify: true
  });

  // Load initial window state
  useEffect(() => {
    const loadWindowState = async () => {
      try {
        const state = await invoke<WindowState>('get_window_state');
        setWindowState(state);
      } catch (err) {
        console.error('Failed to load window state:', err);
        setError('Failed to load window state');
      }
    };

    loadWindowState();
  }, []);

  const handleMinimize = useCallback(async () => {
    try {
      setError(null);
      await invoke('minimize_to_dot');
      setWindowState(prev => ({ ...prev, isMinimized: true }));
    } catch (error) {
      console.error('Failed to minimize:', error);
      setError('Failed to minimize window');
    }
  }, []);



  const handleToggleMute = useCallback(async () => {
    try {
      setError(null);
      const newMuteState = !windowState.isMuted;
      await invoke('toggle_system_audio', { mute: newMuteState });
      setWindowState(prev => ({ ...prev, isMuted: newMuteState }));
    } catch (error) {
      console.error('Failed to toggle mute:', error);
      setError('Failed to toggle audio mute');
    }
  }, [windowState.isMuted]);

  const handleClose = useCallback(async () => {
    try {
      const window = getCurrentWindow();
      await window.close();
    } catch (error) {
      console.error('Failed to close window:', error);
      setError('Failed to close window');
    }
  }, []);

  const handleIframeLoad = useCallback((service: 'youtube' | 'spotify') => {
    setLoadingStates(prev => ({ ...prev, [service]: false }));
  }, []);

  return (
    <div className="app">
      {/* Error Display */}
      {error && (
        <div className="error-banner">
          <span>{error}</span>
          <button onClick={() => setError(null)}>✕</button>
        </div>
      )}

      {/* Custom Window Controls */}
      <div className="window-controls">
        <button
          className="control-btn mute-btn"
          onClick={handleToggleMute}
          title={windowState.isMuted ? 'Unmute' : 'Mute'}
          disabled={windowState.isMinimized}
        >
          {windowState.isMuted ? '🔇' : '🔊'}
        </button>
        <button
          className="control-btn minimize-btn"
          onClick={handleMinimize}
          title="Minimize to dot"
        >
          ⚫
        </button>
        <button
          className="control-btn close-btn"
          onClick={handleClose}
          title="Close"
        >
          ✕
        </button>
      </div>

      {/* Split View Container */}
      <div className="split-container">
        <div className="view-panel youtube-panel">
          <div className="panel-header">
            <h3>YouTube</h3>
          </div>
          <div className={`webview-container ${!loadingStates.youtube ? 'loaded' : ''}`}>
            <iframe
              src="https://www.youtube.com"
              title="YouTube"
              className="webview"
              allow="autoplay; encrypted-media; picture-in-picture"
              allowFullScreen
              onLoad={() => handleIframeLoad('youtube')}
            />
          </div>
        </div>

        <div className="view-panel spotify-panel">
          <div className="panel-header">
            <h3>Spotify</h3>
          </div>
          <div className={`webview-container ${!loadingStates.spotify ? 'loaded' : ''}`}>
            <iframe
              src="https://open.spotify.com"
              title="Spotify"
              className="webview"
              allow="autoplay; encrypted-media"
              onLoad={() => handleIframeLoad('spotify')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;

{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 939180888710801184, "deps": [[3150220818285335163, "url", false, 1270172146973436016], [6913375703034175521, "build_script_build", false, 6991486871027055811], [8319709847752024821, "uuid1", false, 11798349677673730365], [9122563107207267705, "dyn_clone", false, 8694938275405708125], [9689903380558560274, "serde", false, 7128043410522221942], [14923790796823607459, "indexmap", false, 5213176128948876865], [15367738274754116744, "serde_json", false, 9701281561460897278], [16071897500792579091, "schemars_derive", false, 2303142970877198180]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-510435dfd16b1e24\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
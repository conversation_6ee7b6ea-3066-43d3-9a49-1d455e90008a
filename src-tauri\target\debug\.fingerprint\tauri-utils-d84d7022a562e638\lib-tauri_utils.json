{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 11583813680109437787, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3295892275272038733], [3150220818285335163, "url", false, 6908494124195313949], [3191507132440681679, "serde_untagged", false, 7914702612671807769], [4071963112282141418, "serde_with", false, 6780906884518322936], [4899080583175475170, "semver", false, 10013841326225347877], [5986029879202738730, "log", false, 11329875774177104672], [6606131838865521726, "ctor", false, 1190915132638082049], [7170110829644101142, "json_patch", false, 10989428591600042546], [8319709847752024821, "uuid", false, 3143721132148186409], [9010263965687315507, "http", false, 7531794789693008200], [9451456094439810778, "regex", false, 15096131397074870431], [9556762810601084293, "brotli", false, 5690539569629040021], [9689903380558560274, "serde", false, 4745128726568703706], [10806645703491011684, "thiserror", false, 2502066569376588129], [11989259058781683633, "dunce", false, 4683714416206784932], [13625485746686963219, "anyhow", false, 13352825885805927492], [15367738274754116744, "serde_json", false, 7736598212815895422], [15609422047640926750, "toml", false, 767820504169714436], [15622660310229662834, "walkdir", false, 4218211331351021139], [15932120279885307830, "memchr", false, 11792158447639600885], [17146114186171651583, "infer", false, 13857897655402862567], [17155886227862585100, "glob", false, 9965666631543504377], [17186037756130803222, "phf", false, 4609505220296689147]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-d84d7022a562e638\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
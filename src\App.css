* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  color: white;
  position: relative;
}

/* Error Banner */
.error-banner {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: #e74c3c;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.error-banner button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-banner button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

/* Custom Window Controls */
.window-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 30px;
  background: #2d2d2d;
  padding: 0 10px;
  gap: 8px;
  -webkit-app-region: drag;
  user-select: none;
}

.control-btn {
  -webkit-app-region: no-drag;
  background: transparent;
  border: none;
  color: white;
  font-size: 14px;
  width: 30px;
  height: 20px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: #e74c3c;
}

.mute-btn:hover {
  background: #f39c12;
}

.minimize-btn:hover {
  background: #3498db;
}

/* Split View Container */
.split-container {
  flex: 1;
  display: flex;
  height: calc(100vh - 30px);
}

.view-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #444;
}

.view-panel:last-child {
  border-right: none;
}

.panel-header {
  height: 40px;
  background: #333;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  font-size: 14px;
  font-weight: 500;
  color: #ccc;
}

.webview-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.webview {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  transition: opacity 0.3s ease;
}

.webview:disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Loading state */
.webview-container::before {
  content: 'Loading...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 14px;
  z-index: 1;
}

.webview-container.loaded::before {
  display: none;
}

/* YouTube Panel Specific */
.youtube-panel .panel-header {
  background: #ff0000;
}

.youtube-panel .panel-header h3 {
  color: white;
}

/* Spotify Panel Specific */
.spotify-panel .panel-header {
  background: #1db954;
}

.spotify-panel .panel-header h3 {
  color: white;
}

/* Minimized State */
.app.minimized {
  width: 5px;
  height: 5px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #333;
  border-radius: 50%;
  cursor: pointer;
}

.app.minimized .window-controls,
.app.minimized .split-container {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 800px) {
  .split-container {
    flex-direction: column;
  }
  
  .view-panel {
    border-right: none;
    border-bottom: 1px solid #444;
  }
  
  .view-panel:last-child {
    border-bottom: none;
  }
}

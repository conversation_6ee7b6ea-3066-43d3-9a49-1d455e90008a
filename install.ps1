# YSViewer Installation Script for Windows
# This script installs the necessary prerequisites and builds the application

Write-Host "YSViewer Installation Script" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Warning: Running without administrator privileges. Some installations may fail." -ForegroundColor Yellow
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Node.js
Write-Host "Checking Node.js..." -ForegroundColor Cyan
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "Node.js found: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Write-Host "Press any key to open the Node.js download page..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Start-Process "https://nodejs.org/"
    exit 1
}

# Check npm
Write-Host "Checking npm..." -ForegroundColor Cyan
if (Test-Command "npm") {
    $npmVersion = npm --version
    Write-Host "npm found: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "npm not found. Please reinstall Node.js." -ForegroundColor Red
    exit 1
}

# Check Rust
Write-Host "Checking Rust..." -ForegroundColor Cyan
if (Test-Command "cargo") {
    $rustVersion = cargo --version
    Write-Host "Rust found: $rustVersion" -ForegroundColor Green
} else {
    Write-Host "Rust not found. Installing Rust..." -ForegroundColor Yellow
    try {
        # Download and install Rust
        Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "$env:TEMP\rustup-init.exe"
        Start-Process -FilePath "$env:TEMP\rustup-init.exe" -ArgumentList "-y" -Wait
        
        # Refresh environment variables
        # Add Rust's cargo bin to the current session's PATH to make it available immediately.
        $cargoBinPath = Join-Path $env:USERPROFILE ".cargo\bin"
        if (-not ($env:Path.Split(';') -icontains $cargoBinPath)) {
            $env:Path = "$($cargoBinPath);$($env:Path)"
            Write-Host "Added '$cargoBinPath' to PATH for this session." -ForegroundColor DarkGray
        }
        
        if (Test-Command "cargo") {
            Write-Host "Rust installed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Rust installation may have failed. Please restart your terminal and try again." -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "Failed to install Rust automatically. Please install manually from https://rustup.rs/" -ForegroundColor Red
        Start-Process "https://rustup.rs/"
        exit 1
    }
}

# Check for MSVC Build Tools
Write-Host "Checking for MSVC Build Tools..." -ForegroundColor Cyan
$vsWherePath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vsWherePath) {
    # Check for the C++ build tools workload
    $vsInstallation = & $vsWherePath -latest -products * -requires Microsoft.VisualStudio.Workload.NativeDesktop -property installationPath -ErrorAction SilentlyContinue
    if ($vsInstallation) {
        Write-Host "MSVC Build Tools found at: $vsInstallation" -ForegroundColor Green
    } else {
        Write-Host "MSVC Build Tools workload not found." -ForegroundColor Yellow
        Write-Host "Please install 'Desktop development with C++' from the Visual Studio Installer." -ForegroundColor Yellow
        Write-Host "You can download it from: https://visualstudio.microsoft.com/visual-cpp-build-tools/" -ForegroundColor Yellow
        Write-Host "After installation, please restart your terminal and run this script again." -ForegroundColor Yellow
        Start-Process "https://visualstudio.microsoft.com/visual-cpp-build-tools/"
        exit 1
    }
} else {
    Write-Host "Visual Studio Installer not found. MSVC Build Tools are likely missing." -ForegroundColor Yellow
    Write-Host "Please install 'Desktop development with C++' from the Visual Studio Installer: https://visualstudio.microsoft.com/visual-cpp-build-tools/" -ForegroundColor Yellow
    Start-Process "https://visualstudio.microsoft.com/visual-cpp-build-tools/"
    exit 1
}

# Check Tauri CLI
Write-Host "Checking Tauri CLI..." -ForegroundColor Cyan
if (Test-Command "cargo-tauri") {
    Write-Host "Tauri CLI found" -ForegroundColor Green
} else {
    Write-Host "Installing Tauri CLI..." -ForegroundColor Yellow
    try {
        cargo install tauri-cli --version '^2.0.0' --locked
        Write-Host "Tauri CLI installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Failed to install Tauri CLI. Please run: cargo install tauri-cli --version '^2.0.0' --locked" -ForegroundColor Red
        exit 1
    }
}

# Install npm dependencies
Write-Host "Installing npm dependencies..." -ForegroundColor Cyan
try {
    npm install
    Write-Host "npm dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to install npm dependencies." -ForegroundColor Red
    exit 1
}

# Build the application
Write-Host "Building the application..." -ForegroundColor Cyan
try {
    npm run build
    Write-Host "Frontend built successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to build frontend." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""
Write-Host "To run the application in development mode:" -ForegroundColor Cyan
Write-Host "  npm run tauri:dev" -ForegroundColor White
Write-Host ""
Write-Host "To build the application for production:" -ForegroundColor Cyan
Write-Host "  npm run tauri:build" -ForegroundColor White
Write-Host ""
Write-Host "Note: The first build may take several minutes as Rust compiles dependencies." -ForegroundColor Yellow

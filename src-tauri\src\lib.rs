use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::{interval, Duration};

mod audio;
mod window_manager;
mod s3_client;
mod playlist_manager;

use audio::AudioController;
use window_manager::WindowManager;
use playlist_manager::{PlaylistManager, VideoEntry};

#[derive(Debug, Serialize, Deserialize)]
pub struct WindowState {
    is_minimized: bool,
    is_muted: bool,
}

// Global state
pub struct AppState {
    audio_controller: Arc<Mutex<AudioController>>,
    window_manager: Arc<Mutex<WindowManager>>,
    window_state: Arc<Mutex<WindowState>>,
    playlist_manager: Arc<Mutex<Option<PlaylistManager>>>,
}

impl AppState {
    pub fn new() -> Self {
        Self {
            audio_controller: Arc::new(Mutex::new(AudioController::new())),
            window_manager: Arc::new(Mutex::new(WindowManager::new())),
            window_state: Arc::new(Mutex::new(WindowState {
                is_minimized: false,
                is_muted: false,
            })),
            playlist_manager: Arc::new(Mutex::new(None)),
        }
    }
}

#[tauri::command]
async fn toggle_system_audio(
    state: tauri::State<'_, AppState>,
    mute: bool,
) -> Result<bool, String> {
    let audio_controller = state.audio_controller.clone();
    let mut controller = audio_controller.lock().await;

    match controller.set_mute(mute) {
        Ok(_) => {
            let mut window_state = state.window_state.lock().await;
            window_state.is_muted = mute;
            Ok(mute)
        }
        Err(e) => Err(format!("Failed to toggle audio: {}", e)),
    }
}

#[tauri::command]
async fn minimize_to_dot(
    app_handle: AppHandle,
    state: tauri::State<'_, AppState>,
) -> Result<(), String> {
    let window = app_handle.get_webview_window("main").ok_or("Window not found")?;
    let window_manager = state.window_manager.clone();
    let mut manager = window_manager.lock().await;

    match manager.minimize_to_dot(&window) {
        Ok(_) => {
            let mut window_state = state.window_state.lock().await;
            window_state.is_minimized = true;
            Ok(())
        }
        Err(e) => Err(format!("Failed to minimize: {}", e)),
    }
}

#[tauri::command]
async fn restore_from_dot(
    app_handle: AppHandle,
    state: tauri::State<'_, AppState>,
) -> Result<(), String> {
    let window = app_handle.get_webview_window("main").ok_or("Window not found")?;
    let window_manager = state.window_manager.clone();
    let mut manager = window_manager.lock().await;

    match manager.restore_from_dot(&window) {
        Ok(_) => {
            let mut window_state = state.window_state.lock().await;
            window_state.is_minimized = false;
            Ok(())
        }
        Err(e) => Err(format!("Failed to restore: {}", e)),
    }
}

#[tauri::command]
async fn get_window_state(state: tauri::State<'_, AppState>) -> Result<WindowState, String> {
    let window_state = state.window_state.lock().await;
    Ok(WindowState {
        is_minimized: window_state.is_minimized,
        is_muted: window_state.is_muted,
    })
}

#[tauri::command]
async fn get_next_video(state: tauri::State<'_, AppState>) -> Result<Option<VideoEntry>, String> {
    let playlist_manager = state.playlist_manager.clone();
    let mut manager_guard = playlist_manager.lock().await;

    if let Some(ref mut manager) = *manager_guard {
        Ok(manager.get_next_mandatory_video())
    } else {
        Err("Playlist manager not initialized".to_string())
    }
}

#[tauri::command]
async fn update_video_progress(
    state: tauri::State<'_, AppState>,
    url: String,
    progress: f64,
) -> Result<(), String> {
    let playlist_manager = state.playlist_manager.clone();
    let mut manager_guard = playlist_manager.lock().await;

    if let Some(ref mut manager) = *manager_guard {
        manager.update_watch_progress(&url, progress);
        manager.save_playlist().await.map_err(|e| e.to_string())?;
        Ok(())
    } else {
        Err("Playlist manager not initialized".to_string())
    }
}

#[tauri::command]
async fn get_random_videos(state: tauri::State<'_, AppState>) -> Result<Vec<VideoEntry>, String> {
    let playlist_manager = state.playlist_manager.clone();
    let mut manager_guard = playlist_manager.lock().await;

    if let Some(ref mut manager) = *manager_guard {
        Ok(manager.get_random_videos_for_today())
    } else {
        Err("Playlist manager not initialized".to_string())
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = AppState::new();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            toggle_system_audio,
            minimize_to_dot,
            restore_from_dot,
            get_window_state,
            get_next_video,
            update_video_progress,
            get_random_videos
        ])
        .setup(|app| {
            let window = app.get_webview_window("main").unwrap();
            let app_handle_clone = app.handle().clone();

            // Initialize playlist manager
            let state = app.state::<AppState>();
            let playlist_manager_arc = state.playlist_manager.clone();

            tokio::spawn(async move {
                match PlaylistManager::new().await {
                    Ok(manager) => {
                        let mut guard = playlist_manager_arc.lock().await;
                        *guard = Some(manager);
                        println!("Playlist manager initialized successfully");
                    }
                    Err(e) => {
                        eprintln!("Failed to initialize playlist manager: {}", e);
                    }
                }
            });

            // Set up window event handlers
            window.on_window_event(move |event| {
                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        // Handle close event - for now just close normally
                        // Later we can add system tray functionality
                        println!("Window close requested");
                    }
                    _ => {}
                }
            });

            // Start automated playback scheduler
            tokio::spawn(async move {
                start_playback_scheduler(app_handle_clone).await;
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

async fn start_playback_scheduler(app_handle: AppHandle) {
    let mut interval = interval(Duration::from_secs(3600)); // Check every hour

    loop {
        interval.tick().await;

        // Get the app state
        if let Some(state) = app_handle.try_state::<AppState>() {
            let playlist_manager = state.playlist_manager.clone();
            let mut manager_guard = playlist_manager.lock().await;

            if let Some(ref mut manager) = *manager_guard {
                // Check if we need to play mandatory videos
                if let Some(video) = manager.get_next_mandatory_video() {
                    // Emit event to frontend to play video
                    let _ = app_handle.emit("play_video", &video);
                }

                // Save progress
                let _ = manager.save_playlist().await;
            }
        }
    }
}

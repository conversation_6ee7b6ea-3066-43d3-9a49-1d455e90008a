{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 12257821684021708097, "deps": [[376837177317575824, "build_script_build", false, 2958693392210136733], [4143744114649553716, "raw_window_handle", false, 10639854346359357115], [5986029879202738730, "log", false, 11329875774177104672], [10281541584571964250, "windows_sys", false, 2371423468900272983]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-e46eabb76875dace\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
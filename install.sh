#!/bin/bash

# YSViewer Installation Script for macOS/Linux
# This script installs the necessary prerequisites and builds the application

echo "YSViewer Installation Script"
echo "============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js
echo -e "${CYAN}Checking Node.js...${NC}"
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}Node.js found: $NODE_VERSION${NC}"
else
    echo -e "${RED}Node.js not found. Please install Node.js from https://nodejs.org/${NC}"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "On macOS, you can also install using Homebrew: brew install node"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "On Ubuntu/Debian: sudo apt-get install nodejs npm"
        echo "On CentOS/RHEL: sudo yum install nodejs npm"
    fi
    exit 1
fi

# Check npm
echo -e "${CYAN}Checking npm...${NC}"
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}npm found: $NPM_VERSION${NC}"
else
    echo -e "${RED}npm not found. Please reinstall Node.js.${NC}"
    exit 1
fi

# Check Rust
echo -e "${CYAN}Checking Rust...${NC}"
if command_exists cargo; then
    RUST_VERSION=$(cargo --version)
    echo -e "${GREEN}Rust found: $RUST_VERSION${NC}"
else
    echo -e "${YELLOW}Rust not found. Installing Rust...${NC}"
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    
    # Source the cargo environment
    source ~/.cargo/env
    
    if command_exists cargo; then
        echo -e "${GREEN}Rust installed successfully!${NC}"
    else
        echo -e "${RED}Rust installation failed. Please install manually from https://rustup.rs/${NC}"
        exit 1
    fi
fi

# Install platform-specific dependencies
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${CYAN}Checking Xcode Command Line Tools...${NC}"
    if ! xcode-select -p &> /dev/null; then
        echo -e "${YELLOW}Installing Xcode Command Line Tools...${NC}"
        xcode-select --install
        echo "Please complete the Xcode Command Line Tools installation and run this script again."
        exit 1
    else
        echo -e "${GREEN}Xcode Command Line Tools found${NC}"
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${CYAN}Checking build dependencies...${NC}"
    if command_exists apt-get; then
        echo -e "${YELLOW}Installing build dependencies...${NC}"
        sudo apt-get update
        sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
    elif command_exists yum; then
        echo -e "${YELLOW}Installing build dependencies...${NC}"
        sudo yum groupinstall -y "C Development Tools and Libraries"
        sudo yum install -y webkit2gtk3-devel openssl-devel curl wget libappindicator-gtk3-devel librsvg2-devel
    else
        echo -e "${YELLOW}Please install the required build dependencies for your Linux distribution${NC}"
    fi
fi

# Check Tauri CLI
echo -e "${CYAN}Checking Tauri CLI...${NC}"
if command_exists cargo-tauri; then
    echo -e "${GREEN}Tauri CLI found${NC}"
else
    echo -e "${YELLOW}Installing Tauri CLI...${NC}"
    cargo install tauri-cli --version '^2.0.0' --locked
    if command_exists cargo-tauri; then
        echo -e "${GREEN}Tauri CLI installed successfully!${NC}"
    else
        echo -e "${RED}Failed to install Tauri CLI${NC}"
        exit 1
    fi
fi

# Install npm dependencies
echo -e "${CYAN}Installing npm dependencies...${NC}"
if npm install; then
    echo -e "${GREEN}npm dependencies installed successfully!${NC}"
else
    echo -e "${RED}Failed to install npm dependencies${NC}"
    exit 1
fi

# Build the application
echo -e "${CYAN}Building the application...${NC}"
if npm run build; then
    echo -e "${GREEN}Frontend built successfully!${NC}"
else
    echo -e "${RED}Failed to build frontend${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}Installation completed successfully!${NC}"
echo "====================================="
echo ""
echo -e "${CYAN}To run the application in development mode:${NC}"
echo "  npm run tauri:dev"
echo ""
echo -e "${CYAN}To build the application for production:${NC}"
echo "  npm run tauri:build"
echo ""
echo -e "${YELLOW}Note: The first build may take several minutes as Rust compiles dependencies.${NC}"

{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 3451261593873266600, "deps": [[376837177317575824, "softbuffer", false, 5942243387505979255], [2013030631243296465, "webview2_com", false, 4629790111290470228], [2671782512663819132, "tauri_utils", false, 11763592116179359971], [3150220818285335163, "url", false, 6908494124195313949], [3722963349756955755, "once_cell", false, 7508801457861560324], [4143744114649553716, "raw_window_handle", false, 10639854346359357115], [5986029879202738730, "log", false, 11329875774177104672], [6089812615193535349, "tauri_runtime", false, 4324711846368940599], [8826339825490770380, "tao", false, 16713905260865135943], [9010263965687315507, "http", false, 7531794789693008200], [9141053277961803901, "wry", false, 14256868045317903499], [11599800339996261026, "build_script_build", false, 10631007683750238991], [14585479307175734061, "windows", false, 16597157699585629547]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-f2c629a952c310f8\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
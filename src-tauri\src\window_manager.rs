use tauri::{PhysicalPosition, PhysicalSize, Window};
use std::error::Error;
use std::fmt;

#[derive(Debug)]
pub struct WindowError(String);

impl fmt::Display for WindowError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Window error: {}", self.0)
    }
}

impl Error for WindowError {}

pub struct WindowManager {
    original_size: Option<PhysicalSize<u32>>,
    original_position: Option<PhysicalPosition<i32>>,
    is_minimized_to_dot: bool,
}

impl WindowManager {
    pub fn new() -> Self {
        Self {
            original_size: None,
            original_position: None,
            is_minimized_to_dot: false,
        }
    }

    pub fn minimize_to_dot(&mut self, window: &Window) -> Result<(), WindowError> {
        if self.is_minimized_to_dot {
            return Ok(());
        }

        // Store current window state
        self.original_size = window.outer_size().ok();
        self.original_position = window.outer_position().ok();

        // Get screen center position for the dot
        let screen_center = self.get_screen_center()?;
        
        // Set window to 5x5 pixels at screen center
        window
            .set_size(PhysicalSize::new(5, 5))
            .map_err(|e| WindowError(format!("Failed to set window size: {}", e)))?;

        window
            .set_position(PhysicalPosition::new(
                screen_center.0 - 2, // Center the 5px dot
                screen_center.1 - 2,
            ))
            .map_err(|e| WindowError(format!("Failed to set window position: {}", e)))?;

        // Make window always on top when minimized to dot
        window
            .set_always_on_top(true)
            .map_err(|e| WindowError(format!("Failed to set always on top: {}", e)))?;

        // Remove window decorations if not already done
        window
            .set_decorations(false)
            .map_err(|e| WindowError(format!("Failed to remove decorations: {}", e)))?;

        self.is_minimized_to_dot = true;
        Ok(())
    }

    pub fn restore_from_dot(&mut self, window: &Window) -> Result<(), WindowError> {
        if !self.is_minimized_to_dot {
            return Ok(());
        }

        // Restore original size and position
        if let Some(size) = self.original_size {
            window
                .set_size(size)
                .map_err(|e| WindowError(format!("Failed to restore window size: {}", e)))?;
        } else {
            // Default size if original wasn't stored
            window
                .set_size(PhysicalSize::new(1400, 900))
                .map_err(|e| WindowError(format!("Failed to set default window size: {}", e)))?;
        }

        if let Some(position) = self.original_position {
            window
                .set_position(position)
                .map_err(|e| WindowError(format!("Failed to restore window position: {}", e)))?;
        } else {
            // Center window if original position wasn't stored
            window
                .center()
                .map_err(|e| WindowError(format!("Failed to center window: {}", e)))?;
        }

        // Remove always on top
        window
            .set_always_on_top(false)
            .map_err(|e| WindowError(format!("Failed to remove always on top: {}", e)))?;

        // Ensure decorations are still off (frameless)
        window
            .set_decorations(false)
            .map_err(|e| WindowError(format!("Failed to maintain frameless state: {}", e)))?;

        // Bring window to front
        window
            .set_focus()
            .map_err(|e| WindowError(format!("Failed to focus window: {}", e)))?;

        self.is_minimized_to_dot = false;
        Ok(())
    }

    fn get_screen_center(&self) -> Result<(i32, i32), WindowError> {
        // Platform-specific screen center calculation
        #[cfg(windows)]
        return self.get_screen_center_windows();
        
        #[cfg(target_os = "macos")]
        return self.get_screen_center_macos();
        
        #[cfg(not(any(windows, target_os = "macos")))]
        Ok((960, 540)) // Default fallback
    }

    #[cfg(windows)]
    fn get_screen_center_windows(&self) -> Result<(i32, i32), WindowError> {
        use windows::Win32::UI::WindowsAndMessaging::{GetSystemMetrics, SM_CXSCREEN, SM_CYSCREEN};

        unsafe {
            let screen_width = GetSystemMetrics(SM_CXSCREEN);
            let screen_height = GetSystemMetrics(SM_CYSCREEN);
            
            if screen_width == 0 || screen_height == 0 {
                return Err(WindowError("Failed to get screen dimensions".to_string()));
            }
            
            Ok((screen_width / 2, screen_height / 2))
        }
    }

    #[cfg(target_os = "macos")]
    fn get_screen_center_macos(&self) -> Result<(i32, i32), WindowError> {
        use cocoa::appkit::NSScreen;
        use cocoa::base::{id, nil};
        use cocoa::foundation::{NSAutoreleasePool, NSRect};
        use objc::{msg_send, sel, sel_impl};

        unsafe {
            let pool = NSAutoreleasePool::new(nil);
            
            let main_screen: id = msg_send![NSScreen::class(), mainScreen];
            if main_screen == nil {
                pool.drain();
                return Err(WindowError("Failed to get main screen".to_string()));
            }
            
            let frame: NSRect = msg_send![main_screen, frame];
            pool.drain();
            
            Ok((
                (frame.size.width / 2.0) as i32,
                (frame.size.height / 2.0) as i32,
            ))
        }
    }

    pub fn is_minimized(&self) -> bool {
        self.is_minimized_to_dot
    }
}

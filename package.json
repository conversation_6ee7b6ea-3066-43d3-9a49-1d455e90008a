{"name": "ysviewer", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "check": "tsc --noEmit"}, "dependencies": {"@tauri-apps/cli": "^2.6.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-shell": "^2.3.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.0.2", "vite": "^4.4.4"}}